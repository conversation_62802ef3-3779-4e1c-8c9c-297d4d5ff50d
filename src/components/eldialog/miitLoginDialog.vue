<template>
  <div class="miit-login-container">
    <!-- 登录对话框 -->
    <el-dialog
        title="工信部统一身份认证系统登录"
        :visible.sync="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="miit-login-dialog"
    >
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm" label-width="120px">
        <el-form-item label="用户名" prop="username">
          <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :disabled="isProcessing"
              @keyup.enter.native="handleEnterKey"
          >
            <i slot="prefix" class="el-icon-user"></i>
          </el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
              type="password"
              v-model="loginForm.password"
              placeholder="请输入密码"
              :disabled="isProcessing"
              show-password
              @keyup.enter.native="handleEnterKey"
          >
            <i slot="prefix" class="el-icon-lock"></i>
          </el-input>
        </el-form-item>

        <el-form-item label="短信验证码" prop="smsCode">
          <el-input
              v-model="loginForm.smsCode"
              placeholder="请先获取验证码"
              :disabled="!smsCodeEnabled || isProcessing"
              @keyup.enter.native="handleLogin"
          >
            <el-button
                slot="append"
                :disabled="!canGetSmsCode"
                :loading="isGettingSms"
                @click="getSmsCode"
                type="primary"
            >
              {{ smsButtonText }}
            </el-button>
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="isProcessing">取消</el-button>
        <el-button
            type="primary"
            @click="handleLoginSuccess(`https://txjs.miit.gov.cn/?cssSsoTicket=e0f64a8d4223484883b45998df126d36`)"

        >

          登录
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import submitMixin from "@/util/submitMixin";
import MiitLoginApi from '@/validate/miitLogin/utils/miitLoginApi';
import { EamMessage } from '@/util/EamElementExt';

export default {
  name: 'miitLoginDialog',
  mixins: [submitMixin],
  props: {
    dialogVisible: { type: Boolean, default: false },
  },
  computed: {
    canGetSmsCode() {
      return this.loginForm.username &&
          this.loginForm.password &&
          !this.isGettingSms &&
          this.smsCountdown === 0;
    },

    canLogin() {
      return this.loginForm.username &&
          this.loginForm.password &&
          this.loginForm.smsCode &&
          !this.isLogging;
    },

    smsButtonText() {
      if (this.isGettingSms) {
        return '发送中...';
      } else if (this.smsCountdown > 0) {
        return `${this.smsCountdown}s后重试`;
      } else {
        return '获取验证码';
      }
    }
  },
  mounted() {
  },
  data() {
    return {

      // 状态控制
      isChecking: false,
      isGettingSms: false,
      isLogging: false,
      isProcessing: false,

      // 短信验证码相关
      smsCodeEnabled: false,
      smsCountdown: 0,
      smsTimer: null,

      // 登录表单
      loginForm: {
        username: '',
        password: '',
        smsCode: '',
        toUrl: 'http://txjs.miit.gov.cn'
      },

      // 表单验证规则
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6位', trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '验证码应为6位数字', trigger: 'blur' }
        ]
      },

      // API实例
      loginApi: new MiitLoginApi()
    }
  },
  methods: {
    /**
     * 验证指定字段
     */
    async validateFields(fields) {
      try {
        await this.$refs.loginForm.validateField(fields);
        return true;
      } catch (error) {
        return false;
      }
    },

    /**
     * 获取短信验证码
     */
    async getSmsCode() {
      // 表单验证
      const valid = await this.validateFields(['username', 'password']);
      if (!valid) return;

      this.isGettingSms = true;
      this.isProcessing = true;

      try {
        const result = await this.loginApi.getSmsCode({
          username: this.loginForm.username,
          password: this.loginForm.password
        });
        if (result && result.result === 0) {
          this.smsCodeEnabled = true;
          this.startSmsCountdown();

          // 聚焦到验证码输入框
          this.$nextTick(() => {
            this.$refs.loginForm.$el.querySelector('input[placeholder*="验证码"]').focus();
          });

          EamMessage.success( "已发送到手机号：" + result.msg + "，请注意查收");
        } else {
          EamMessage.error(result.msg);
        }
      } catch (error) {
        plugin.error('获取短信验证码失败:', error);
        EamMessage.error('获取短信验证码失败');
      } finally {
        this.isGettingSms = false;
        this.isProcessing = false;
      }
    },

    /**
     * 执行登录
     */
    async handleLogin() {
      // 表单验证
      const valid = await this.validateForm();
      if (!valid) return;

      this.isLogging = true;
      this.isProcessing = true;

      try {
        const result = await this.loginApi.login({
          username: this.loginForm.username,
          password: this.loginForm.password,
          smsCode: this.loginForm.smsCode,
          toUrl: this.loginForm.toUrl
        });

        if (result && result.result === 0) {
          const redirectUrl = result.msg; // 重定向地址
          plugin.log('登录成功，开始处理重定向:', redirectUrl);
          const newRedirectUrl = redirectUrl.replace("http", "https");
          // 处理重定向和获取cookies
          await this.handleLoginSuccess(newRedirectUrl);
        } else {
          EamMessage.error(result.msg);
        }
      } catch (error) {
        plugin.error('登录失败:', error);
        EamMessage.error('登录失败');
      } finally {
        this.isLogging = false;
        this.isProcessing = false;
      }
    },

    /**
     * 处理回车键
     */
    handleEnterKey() {
      if (this.canGetSmsCode && !this.smsCodeEnabled) {
        this.getSmsCode();
      } else if (this.canLogin) {
        this.handleLogin();
      }
    },

    /**
     * 开始短信倒计时
     */
    startSmsCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer);
          this.smsTimer = null;
        }
      }, 1000);
    },
    /**
     * 处理登录成功后的重定向和cookie获取
     * @param {string} redirectUrl - 重定向URL
     */
    async handleLoginSuccess(redirectUrl) {
      try {
        plugin.log('正在处理登录重定向...');
        const cookieResult = await this.loginApi.getRedirectCookies(redirectUrl);
        debugger
        if (cookieResult.success) {
          plugin.log('成功获取cookies:', cookieResult.cookies);
          // 存储cookies到本地存储或状态管理
          // this.dialogVisible = false;
          plugin.log(`获取到 ${cookieResult.cookieCount} 个cookies`);
          // 触发登录成功事件，传递cookies
        } else {
          throw new Error(cookieResult.message || 'cookies获取失败');
        }
      } catch (error) {
        plugin.error('处理登录重定向失败:', error);
      }
    },

    /**
     * 表单验证
     */
    async validateForm() {
      try {
        await this.$refs.loginForm.validate();
        return true;
      } catch (error) {
        return false;
      }
    },
  },
}
</script>

<style>
  .miit-login-container {
    .el-form-item {
      margin-bottom: 10px;
    }
    .el-form-item__content .el-input-group {
      vertical-align: inherit;
    }
  }
</style>