import CryptoJS from 'crypto-js';

// 封装带认证的API请求 - 使用fetch避免CORS预检请求
export const callMiitApi = async (config) => {
    try {
        const response = await fetch(config.url, {
            method: config.method || 'POST',
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
            },
            body: config.data,
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.text();
        return { data };
    } catch (error) {
        console.error('请求失败:', error);
        throw error;
    }
}


/**
 * 工信部统一身份认证系统登录封装
 * 网址: https://txjs.miit.gov.cn/sso.action (重定向到 http://ucenter.miit.gov.cn/login.jsp)
 * 使用原生fetch避免CORS预检请求问题
 */
class MiitLoginApi {
    constructor(options = {}) {
        this.baseUrl = 'http://ucenter.miit.gov.cn';
        this.loginUrl = `${this.baseUrl}/login.action`;
        this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;
    }

    /**
     * MD5加密密码 (模拟页面中的md5函数)
     * @param {string} password - 原始密码
     * @returns {string} MD5加密后的密码
     */
    md5Encrypt(password) {
        return CryptoJS.MD5(password).toString();
    }

    /**
     * 获取短信验证码
     * @param {Object} params - 参数对象
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @returns {Promise<Object>} 返回结果
     */
    async getSmsCode({ username, password, logintype = 'sendMessage' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号或密码不能为空！');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数
            const params = new URLSearchParams({
                logintype: logintype,
                username: username.trim(),
                password: encryptedPassword
            });
            const result = await callMiitApi({
                method: 'POST',
                url: this.sendMessageUrl,
                data: params
            });
            return JSON.parse(result.data)
        } catch (error) {
            throw new Error('获取短信验证码失败');
        }
    }

    /**
     * 执行登录
     * @param {Object} params - 登录参数
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} params.smsCode - 短信验证码
     * @param {string} [params.toUrl] - 登录成功后跳转的URL
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @param {string} [params.dialogFlag=''] - 对话框标志
     * @returns {Promise<Object>} 登录结果
     */
    async login({ username, password, smsCode, toUrl, logintype = 'sendMessage', dialogFlag = '' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号和密码不能为空');
            }
            if (!smsCode) {
                throw new Error('短信验证码不能为空');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数 (模拟表单提交)
            const formData = new URLSearchParams({
                username: username.trim(),
                password: encryptedPassword,  // 这里是加密后的密码，对应隐藏字段#pwd
                yznum: smsCode.trim(),
                logintype: logintype,
                toUrl: toUrl || 'http%3A%2F%2Ftxjs.miit.gov.cn',
                flag: '',
                mobile: '',
                getyznum: '',
                dialogFlag: dialogFlag
            });

            const result = await callMiitApi({
                method: 'POST',
                url: this.loginUrl,
                data: formData
            });
            console.log('执行登录请求:', { username, logintype, toUrl });
            return JSON.parse(result.data)
        } catch (error) {
            throw new Error('登录失败');
        }
    }

    /**
     * 仅跟随重定向获取cookies（不调用鉴权API）
     * @param {string} redirectUrl - 重定向URL
     * @returns {Promise<Object>} cookies和重定向信息
     */
    async getRedirectCookies(redirectUrl) {

    }

    /**
     * 使用fetch API的简化重定向处理
     */
    async followRedirectWithFetch(redirectUrl) {
        try {
            console.log('🔧 使用fetch API处理重定向');

            // 使用自动重定向，然后尝试从响应中获取信息
            const response = await fetch(redirectUrl, {
                method: 'GET',
                mode: 'cors',
                redirect: 'follow', // 自动跟随重定向
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });


        } catch (error) {

        }
    }

}

export default MiitLoginApi;
