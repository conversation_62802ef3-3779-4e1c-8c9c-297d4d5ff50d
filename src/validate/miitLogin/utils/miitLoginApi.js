import CryptoJS from 'crypto-js';

// 封装带认证的API请求 - 使用fetch避免CORS预检请求
export const callMiitApi = async (config) => {
    try {
        const response = await fetch(config.url, {
            method: config.method || 'POST',
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
            },
            body: config.data,
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.text();
        return { data };
    } catch (error) {
        console.error('请求失败:', error);
        throw error;
    }
}


/**
 * 工信部统一身份认证系统登录封装
 * 网址: https://txjs.miit.gov.cn/sso.action (重定向到 http://ucenter.miit.gov.cn/login.jsp)
 * 使用原生fetch避免CORS预检请求问题
 */
class MiitLoginApi {
    constructor(options = {}) {
        this.baseUrl = 'http://ucenter.miit.gov.cn';
        this.loginUrl = `${this.baseUrl}/login.action`;
        this.sendMessageUrl = `${this.baseUrl}/sendMessage.action`;
    }

    /**
     * MD5加密密码 (模拟页面中的md5函数)
     * @param {string} password - 原始密码
     * @returns {string} MD5加密后的密码
     */
    md5Encrypt(password) {
        return CryptoJS.MD5(password).toString();
    }

    /**
     * 获取短信验证码
     * @param {Object} params - 参数对象
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @returns {Promise<Object>} 返回结果
     */
    async getSmsCode({ username, password, logintype = 'sendMessage' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号或密码不能为空！');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数
            const params = new URLSearchParams({
                logintype: logintype,
                username: username.trim(),
                password: encryptedPassword
            });
            const result = await callMiitApi({
                method: 'POST',
                url: this.sendMessageUrl,
                data: params
            });
            return JSON.parse(result.data)
        } catch (error) {
            throw new Error('获取短信验证码失败');
        }
    }

    /**
     * 执行登录
     * @param {Object} params - 登录参数
     * @param {string} params.username - 用户名/统一社会信用代码/身份证号
     * @param {string} params.password - 密码
     * @param {string} params.smsCode - 短信验证码
     * @param {string} [params.toUrl] - 登录成功后跳转的URL
     * @param {string} [params.logintype='sendMessage'] - 登录类型
     * @param {string} [params.dialogFlag=''] - 对话框标志
     * @returns {Promise<Object>} 登录结果
     */
    async login({ username, password, smsCode, toUrl, logintype = 'sendMessage', dialogFlag = '' }) {
        try {
            // 验证必填参数
            if (!username || !password) {
                throw new Error('账号和密码不能为空');
            }
            if (!smsCode) {
                throw new Error('短信验证码不能为空');
            }

            // 密码MD5加密
            const encryptedPassword = this.md5Encrypt(password);

            // 构建请求参数 (模拟表单提交)
            const formData = new URLSearchParams({
                username: username.trim(),
                password: encryptedPassword,  // 这里是加密后的密码，对应隐藏字段#pwd
                yznum: smsCode.trim(),
                logintype: logintype,
                toUrl: toUrl || 'http%3A%2F%2Ftxjs.miit.gov.cn',
                flag: '',
                mobile: '',
                getyznum: '',
                dialogFlag: dialogFlag
            });

            const result = await callMiitApi({
                method: 'POST',
                url: this.loginUrl,
                data: formData
            });
            console.log('执行登录请求:', { username, logintype, toUrl });
            return JSON.parse(result.data)
        } catch (error) {
            throw new Error('登录失败');
        }
    }

    /**
     * 仅跟随重定向获取cookies（不调用鉴权API）
     * @param {string} redirectUrl - 重定向URL
     * @returns {Promise<Object>} cookies和重定向信息
     */
    async getRedirectCookies(redirectUrl) {
        try {
            console.log('🍪 开始获取重定向cookies:', redirectUrl);

            // 尝试多种策略来处理CORS问题
            let response;

            try {
                // 策略1: 使用简单请求避免预检请求
                response = await fetch(redirectUrl, {
                    method: 'GET',
                    mode: 'cors',
                    redirect: 'manual', // 不自动跟随重定向
                    credentials: 'include', // 包含cookies
                    headers: {
                        // 只使用简单请求头，避免触发预检请求
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    }
                });
            } catch (corsError) {
                console.warn('⚠️ CORS策略1失败，尝试策略2:', corsError.message);

                try {
                    // 策略2: 不使用credentials
                    response = await fetch(redirectUrl, {
                        method: 'GET',
                        mode: 'cors',
                        redirect: 'manual',
                        credentials: 'omit', // 不包含cookies
                        headers: {
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                        }
                    });
                } catch (corsError2) {
                    console.warn('⚠️ CORS策略2失败，尝试策略3:', corsError2.message);

                    // 策略3: 使用Chrome扩展API（如果可用）
                    const chromeResult = await this.getCookiesWithChromeAPI(redirectUrl);
                    if (chromeResult.success) {
                        return chromeResult;
                    }

                    // 策略4: 使用no-cors模式（最后的备选方案）
                    response = await fetch(redirectUrl, {
                        method: 'GET',
                        mode: 'no-cors',
                        redirect: 'manual',
                        credentials: 'omit'
                    });
                }
            }

            console.log('📡 响应状态:', response.status, response.statusText);
            console.log('📍 响应类型:', response.type);

            // 提取cookies
            const cookies = this.extractCookiesFromResponse(response);

            // 获取重定向位置（如果有）
            const location = response.headers.get('Location');

            // 获取响应体（如果需要）
            let responseText = '';
            try {
                responseText = await response.text();
            } catch (e) {
                console.warn('无法读取响应体:', e.message);
            }

            const result = {
                success: true,
                cookies: cookies,
                cookieCount: Object.keys(cookies).length,
                status: response.status,
                statusText: response.statusText,
                redirectLocation: location,
                responseUrl: response.url,
                responseText: responseText.substring(0, 500), // 只保留前500字符
                headers: this.extractResponseHeaders(response)
            };

            console.log('✅ 成功获取cookies:', result);
            return result;

        } catch (error) {
            console.error('❌ 获取重定向cookies失败:', error);
            return {
                success: false,
                message: error.message,
                error: error.toString(),
                cookies: {},
                cookieCount: 0
            };
        }
    }

    /**
     * 使用fetch API的简化重定向处理（不跟随重定向）
     */
    async followRedirectWithFetch(redirectUrl) {
        try {
            console.log('🔧 使用fetch API处理重定向（单次请求）');

            // 尝试多种策略来处理CORS问题
            let response;

            try {
                // 策略1: 使用简单请求避免预检请求
                response = await fetch(redirectUrl, {
                    method: 'GET',
                    mode: 'cors',
                    redirect: 'manual', // 不自动跟随重定向
                    credentials: 'include',
                    headers: {
                        // 只使用简单请求头
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    }
                });
            } catch (corsError) {
                console.warn('⚠️ CORS策略1失败，尝试策略2:', corsError.message);

                try {
                    // 策略2: 不使用credentials
                    response = await fetch(redirectUrl, {
                        method: 'GET',
                        mode: 'cors',
                        redirect: 'manual',
                        credentials: 'omit',
                        headers: {
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                        }
                    });
                } catch (corsError2) {
                    console.warn('⚠️ CORS策略2失败，尝试no-cors模式:', corsError2.message);

                    // 策略3: 使用no-cors模式
                    response = await fetch(redirectUrl, {
                        method: 'GET',
                        mode: 'no-cors',
                        redirect: 'manual',
                        credentials: 'omit'
                    });
                }
            }

            console.log('📡 单次请求响应:', response.status, response.statusText);

            // 提取cookies和重定向信息
            const cookies = this.extractCookiesFromResponse(response);
            const location = response.headers.get('Location');

            return {
                success: true,
                status: response.status,
                statusText: response.statusText,
                cookies: cookies,
                cookieCount: Object.keys(cookies).length,
                redirectLocation: location,
                responseUrl: response.url,
                headers: this.extractResponseHeaders(response)
            };

        } catch (error) {
            console.error('❌ fetch重定向处理失败:', error);
            return {
                success: false,
                message: error.message,
                error: error.toString(),
                cookies: {},
                cookieCount: 0
            };
        }
    }

    /**
     * 从响应中提取cookies
     * @param {Response} response - fetch响应对象
     * @returns {Object} cookies对象
     */
    extractCookiesFromResponse(response) {
        const cookies = {};

        try {
            // 获取所有Set-Cookie头
            const setCookieHeaders = response.headers.get('Set-Cookie');

            if (setCookieHeaders) {
                // 解析Set-Cookie头
                const cookieStrings = setCookieHeaders.split(',');

                cookieStrings.forEach(cookieString => {
                    const trimmed = cookieString.trim();
                    if (trimmed) {
                        const [nameValue] = trimmed.split(';');
                        if (nameValue && nameValue.includes('=')) {
                            const [name, value] = nameValue.split('=', 2);
                            if (name && value) {
                                cookies[name.trim()] = value.trim();
                            }
                        }
                    }
                });
            }

            // 在Chrome扩展环境中，可能需要通过其他方式获取cookies
            // 这里可以添加额外的cookie提取逻辑
            console.log('🍪 提取到的cookies:', cookies);

        } catch (error) {
            console.warn('⚠️ 提取cookies时出错:', error);
        }

        return cookies;
    }

    /**
     * 提取响应头信息
     * @param {Response} response - fetch响应对象
     * @returns {Object} 响应头对象
     */
    extractResponseHeaders(response) {
        const headers = {};

        try {
            // 提取重要的响应头
            const importantHeaders = [
                'Content-Type',
                'Location',
                'Set-Cookie',
                'Cache-Control',
                'Server',
                'Date'
            ];

            importantHeaders.forEach(headerName => {
                const value = response.headers.get(headerName);
                if (value) {
                    headers[headerName] = value;
                }
            });

            console.log('📋 提取到的响应头:', headers);

        } catch (error) {
            console.warn('⚠️ 提取响应头时出错:', error);
        }

        return headers;
    }

    /**
     * 使用Chrome扩展API获取cookies（如果在扩展环境中）
     * @param {string} url - 目标URL
     * @returns {Promise<Object>} cookies信息
     */
    async getCookiesWithChromeAPI(url) {
        try {
            // 检查是否在Chrome扩展环境中
            if (typeof chrome !== 'undefined' && chrome.cookies) {
                console.log('🔧 使用Chrome扩展API获取cookies');

                const urlObj = new URL(url);
                const cookies = await new Promise((resolve, reject) => {
                    chrome.cookies.getAll({
                        domain: urlObj.hostname
                    }, (cookies) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(cookies);
                        }
                    });
                });

                const cookieObj = {};
                cookies.forEach(cookie => {
                    cookieObj[cookie.name] = cookie.value;
                });

                return {
                    success: true,
                    cookies: cookieObj,
                    cookieCount: cookies.length,
                    method: 'chrome_api'
                };
            } else {
                return {
                    success: false,
                    message: '不在Chrome扩展环境中',
                    method: 'chrome_api'
                };
            }
        } catch (error) {
            console.error('❌ Chrome API获取cookies失败:', error);
            return {
                success: false,
                message: error.message,
                error: error.toString(),
                method: 'chrome_api'
            };
        }
    }

    /**
     * 使用Chrome扩展的特权绕过CORS限制获取cookies
     * @param {string} redirectUrl - 重定向URL
     * @returns {Promise<Object>} cookies和重定向信息
     */
    async getRedirectCookiesWithExtension(redirectUrl) {
        try {
            console.log('🔧 使用Chrome扩展特权获取cookies:', redirectUrl);

            // 检查是否在Chrome扩展环境中
            if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.runtime.id) {
                throw new Error('不在Chrome扩展环境中');
            }

            // 使用Chrome扩展的fetch（绕过CORS）
            const response = await fetch(redirectUrl, {
                method: 'GET',
                redirect: 'manual',
                credentials: 'include'
            });

            console.log('📡 扩展请求响应:', response.status, response.statusText);

            // 提取cookies
            const cookies = this.extractCookiesFromResponse(response);

            // 获取重定向位置
            const location = response.headers.get('Location');

            // 如果没有从响应头获取到cookies，尝试使用Chrome cookies API
            if (Object.keys(cookies).length === 0) {
                const chromeApiResult = await this.getCookiesWithChromeAPI(redirectUrl);
                if (chromeApiResult.success) {
                    return {
                        success: true,
                        cookies: chromeApiResult.cookies,
                        cookieCount: chromeApiResult.cookieCount,
                        status: response.status,
                        statusText: response.statusText,
                        redirectLocation: location,
                        responseUrl: response.url,
                        method: 'chrome_extension_with_api',
                        headers: this.extractResponseHeaders(response)
                    };
                }
            }

            return {
                success: true,
                cookies: cookies,
                cookieCount: Object.keys(cookies).length,
                status: response.status,
                statusText: response.statusText,
                redirectLocation: location,
                responseUrl: response.url,
                method: 'chrome_extension',
                headers: this.extractResponseHeaders(response)
            };

        } catch (error) {
            console.error('❌ Chrome扩展获取cookies失败:', error);
            return {
                success: false,
                message: error.message,
                error: error.toString(),
                cookies: {},
                cookieCount: 0,
                method: 'chrome_extension'
            };
        }
    }

}

export default MiitLoginApi;
