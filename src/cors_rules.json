[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "http://localhost:9527"}, {"header": "Access-Control-Allow-Credentials", "operation": "set", "value": "true"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>, Cache-Control, Pragma, User-Agent"}, {"header": "Access-Control-Expose-Headers", "operation": "set", "value": "Set-<PERSON><PERSON>, Location"}, {"header": "Access-Control-Max-Age", "operation": "set", "value": "86400"}]}, "condition": {"urlFilter": "*://ucenter.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "http://localhost:9527"}, {"header": "Access-Control-Allow-Credentials", "operation": "set", "value": "true"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>, Cache-Control, Pragma, User-Agent"}, {"header": "Access-Control-Expose-Headers", "operation": "set", "value": "Set-<PERSON><PERSON>, Location"}, {"header": "Access-Control-Max-Age", "operation": "set", "value": "86400"}]}, "condition": {"urlFilter": "*://txjs.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 3, "priority": 2, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "http://localhost:9527"}, {"header": "Access-Control-Allow-Credentials", "operation": "set", "value": "true"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>, Cache-Control, Pragma, User-Agent"}, {"header": "Access-Control-Max-Age", "operation": "set", "value": "86400"}]}, "condition": {"urlFilter": "*://txjs.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest"], "requestMethods": ["options"]}}, {"id": 4, "priority": 2, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "http://localhost:9527"}, {"header": "Access-Control-Allow-Credentials", "operation": "set", "value": "true"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>, Cache-Control, Pragma, User-Agent"}, {"header": "Access-Control-Max-Age", "operation": "set", "value": "86400"}]}, "condition": {"urlFilter": "*://ucenter.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest"], "requestMethods": ["options"]}}]