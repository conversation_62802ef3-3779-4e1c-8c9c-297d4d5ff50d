[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "*"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>"}, {"header": "Access-Control-Expose-Headers", "operation": "set", "value": "Set-<PERSON><PERSON>, Location"}]}, "condition": {"urlFilter": "*://ucenter.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "*"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With, Accept, VERSION, <PERSON><PERSON>"}, {"header": "Access-Control-Expose-Headers", "operation": "set", "value": "Set-<PERSON><PERSON>, Location"}]}, "condition": {"urlFilter": "*://txjs.miit.gov.cn/*", "resourceTypes": ["xmlhttprequest"]}}]